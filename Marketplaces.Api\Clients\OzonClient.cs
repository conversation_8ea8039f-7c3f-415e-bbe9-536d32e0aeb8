using System.Text.Json.Serialization;
using Marketplaces.Api.Dtos.Ozon.Nomenclatures;
using Marketplaces.Api.Dtos.Ozon.Price;
using Marketplaces.Api.Dtos.Ozon.Promotes;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Models;
using Marketplaces.Api.Requests;
using Microsoft.Extensions.Options;
using RestSharp;

namespace Marketplaces.Api.Clients;

public class OzonClient(IOptions<AppSettings> options, ILogger<OzonClient> logger)
{
    private readonly AppSettings _appSettings = options.Value;
    private const string OzonDateTimeFormat = "yyyy-MM-ddTHH:mm:ss.ffK";
    private const string GetNomenclaturesUrl = "/v3/product/list";
    private const string GetNomenclaturesInfoUrl = "/v3/product/info/list";
    private const string GetNomenclaturesAttributesUrl = "v4/product/info/attributes";
    private const string GetStocksUrl = "/v4/product/info/stocks";
    private const string UpdateStocksUrl = "v2/products/stocks";
    private const string GetWarehousesUrl = "v1/warehouse/list";
    private const string GetPricesUrl = "/v5/product/info/prices";
    private const string UpdatePricesUrl = "/v1/product/import/prices";
    private const string GetReportUrl = "v3/finance/transaction/list";
    private const string GetPostingUrl = "v3/posting/fbs/list";
    private const string GetPromotesUrl = "v1/actions";
    private const string DeleteProductFromPromotes = "v1/actions/products/deactivate";

    private const int Limit = 100;

    private RestClient GetOzonClient(Shop shop)
    {
        var authentication = shop.GetToken(Marketplace.Ozon) as OzonAuthenticationData
                             ?? throw new BadRequestException("Ozon токен отсутствует");
        var options = new RestClientOptions(_appSettings.OzonUrl);
        var client = new RestClient(options);
        client.AddDefaultHeader("Client-Id", authentication.ClientId);
        client.AddDefaultHeader("Api-Key", authentication.ApiKey);
        return client;
    }

    public async Task<List<NomenclatureInfoDto>> GetNomenclatures(Shop shop)
    {
        var results = new List<NomenclatureInfoDto>();
        var client = GetOzonClient(shop);
        var request = new RestRequest(GetNomenclaturesUrl, Method.Post);
        var requestBody = new GetNomenclaturesListRequestDto(Limit);

        var calculated = 0;
        var fetchedOfferIds = new List<string>();
        while (true)
        {
            request.AddBody(requestBody);

            var response = await client.ExecuteAsync<NomenclaturesListResponseDto>(request);
            if (!response.IsSuccessful || response.Data == null)
            {
                logger.LogError(response.ErrorException, "Error: message: {Message}", response.Content);
                throw new ApiException("Не получилось получить номенклатуры из Ozon");
            }

            fetchedOfferIds.AddRange(response.Data.Result.Items.Select(i => i.OfferId));
            calculated += Limit;
            if (response.Data.Result.Total <= calculated)
            {
                break;
            }

            requestBody.LastId = response.Data.Result.LastId;
        }

        foreach (var offerIds in fetchedOfferIds.Chunk(Limit))
        {
            var infoRequest = new RestRequest(GetNomenclaturesInfoUrl, Method.Post);
            var infoBody = new FilterRequestDto { OfferIds = [.. offerIds] };
            infoRequest.AddBody(infoBody);
            var response = await client.ExecuteAsync<NomenclaturesResponseDto>(infoRequest);
            if (!response.IsSuccessful || response.Data == null)
            {
                logger.LogError(response.ErrorException, "Error: message: {Message}", response.Content);
            }

            results.AddRange(response.Data?.Items ?? []);
        }

        // I need to get attributes 
        var attResults = new List<ResultAttributes>();
        foreach (var chunk in results.Chunk(Limit))
        {
            var attributesRequest = new RestRequest(GetNomenclaturesAttributesUrl, Method.Post);
            var attributesBody = new FilterWithLimitRequestDto(
                new FilterRequestDto() { OfferIds = [.. chunk.Select(c => c.OfferId)] },
                Limit);

            attributesRequest.AddBody(attributesBody);
            var attributesResponse = await client.ExecuteAsync<RootAttribute>(attributesRequest);
            if (!attributesResponse.IsSuccessful || attributesResponse.Data == null)
            {
                logger.LogError(attributesResponse.ErrorException, "Error: message: {Message}",
                    attributesResponse.Content);
            }

            attResults.AddRange(attributesResponse.Data?.Result ?? []);
        }

        foreach (var result in results)
        {
            var attResult = attResults.Find(a => a.OfferId == result.OfferId);
            result.CommonCode = attResult?.ModelInfo.ModelId;
            var sizes = attResult?.Attributes.Find(a => a.Id is 4295 or 4298)?.Values.Select(v => v.Value).ToList();
            if (sizes != null)
            {
                var size = string.Join(',', sizes);
                result.Size = size;
            }
        }

        return results;
    }

    public async Task<List<InternalOzonStockDto>> GetStocks(Shop shop, List<long> ids)
    {
        var client = GetOzonClient(shop);
        var results = new List<InternalOzonStockDto>();
        var request = new RestRequest(GetStocksUrl, Method.Post);


        var idsChunk = ids.Chunk(Limit);
        foreach (var chunk in idsChunk)
        {
            var requestBody = new
            {
                Filter = new
                {
                    product_id = chunk
                },
                Limit = Limit
            };

            request.AddBody(requestBody);
            var response = await client.ExecuteAsync<OzonStockResultDto>(request);
            if (!response.IsSuccessful || response.Data == null)
            {
                logger.LogError(response.ErrorException, "Error: message: {Message}", response.Content);
                throw new ApiException("Не получилось получить номенклатуры из Ozon");
            }

            results.AddRange(response.Data.Items);
        }

        return results;
    }

    public Task UpdateStocks(Shop shop, long warehouseId, string ozonCode, int amount)
    {
        return UpdateStocks(shop, warehouseId, [new UpdateStocksItem(ozonCode, "", amount)]);
    }

    public async Task UpdateStocks(Shop shop, long warehouseId, List<UpdateStocksItem> items)
    {
        if (items.Count == 0)
        {
            return;
        }

        var client = GetOzonClient(shop);
        var request = new RestRequest(UpdateStocksUrl, Method.Post);
        logger.LogInformation($"Записываем в ozon, {string.Join(' ', items)}");

        var requestBody = new
        {
            stocks = items.Select(i => new
            {
                offer_id = i.Code,
                stock = i.FbsAmount,
                quant_size = 1,
                warehouse_id = warehouseId
            })
        };
        request.AddBody(requestBody);
        var response = await client.ExecuteAsync(request);
        if (!response.IsSuccessful)
        {
            throw new BadRequestException("Ошибка при обновлении остатков озона");
        }
    }

    public async Task<List<long>> GetWarehouseIds(Shop shop)
    {
        var client = GetOzonClient(shop);
        var request = new RestRequest(GetWarehousesUrl, Method.Post);
        var response = await client.ExecuteAsync<OzonWarehousesDtoWrapper>(request);
        if (!response.IsSuccessful || response.Data == null)
        {
            throw new BadRequestException("Ошибка при обновлении остатков озона");
        }

        return [.. response.Data.Result.Where(r => r.Status == "created").Select(s => s.WarehouseId)];
    }

    public async Task<List<PriceItemDto>> GetPrices(Shop shop, List<string> codes)
    {
        var client = GetOzonClient(shop);
        var results = new List<PriceItemDto>();
        var request = new RestRequest(GetPricesUrl, Method.Post);
        foreach (var chunk in codes.Chunk(Limit))
        {
            var requestBody = new FilterWithLimitRequestDto(new FilterRequestDto { OfferIds = [.. chunk] }, Limit);
            request.AddBody(requestBody);
            var response = await client.ExecuteAsync<PricesResponseDto>(request);
            if (!response.IsSuccessful || response.Data == null)
            {
                logger.LogError(response.ErrorException, "Error: message: {Message}", response.Content);
                throw new ApiException("Не получилось получить цены из Ozon");
            }

            results.AddRange(response.Data.Items);
        }

        return results;
    }

    public async Task UpdatePrice(Shop shop, List<(string Code, decimal Price)> ozonPrices)
    {
        if (ozonPrices.Count == 0)
        {
            return;
        }

        var client = GetOzonClient(shop);
        var request = new RestRequest(UpdatePricesUrl, Method.Post);
        var requestBody = new UpdatePricesRequestDto
        {
            Prices = [.. ozonPrices.Select(p => new UpdatePriceRequestDto(p.Code, p.Price))]
        };

        request.AddBody(requestBody);
        var response = await client.ExecuteAsync<UpdatePricesResponseDto>(request);
        if (!response.IsSuccessful || response.Data == null || !response.Data.Result[0].Updated)
        {
            logger.LogError(response.ErrorException, "Error: message: {Message}", response.Content);
            throw new BadRequestException("Ошибка при обновлении цены озона", response.ErrorException);
        }
    }

    public async Task<List<Operation>> GetReportDtos(Shop shop, DateTime dateFrom, DateTime dateTo)
    {
        var client = GetOzonClient(shop);
        var request = new RestRequest(GetReportUrl, Method.Post);

        var results = new List<Operation>();

        var requestBody = new
        {
            filter = new
            {
                date = new
                {
                    from = dateFrom.ToString(OzonDateTimeFormat),
                    to = dateTo.ToString(OzonDateTimeFormat),
                },
                transaction_type = "all"
            },
            page_size = 1000,
            page = 1
        };

        request.AddBody(requestBody);
        var response = await client.ExecuteAsync<FinanceTransactionResponse>(request);
        if (!response.IsSuccessful || response.Data?.Result.Operations == null)
        {
            logger.LogError(response.ErrorException, "Error: message: {Message}", response.Content);
            throw new ApiException("Ошибка получения данных по отчетам", response.ErrorException);
        }

        results.AddRange(response.Data.Result.Operations);
        return results;
    }

    public async Task<List<PostingInfo>> GetPostingList(Shop shop)
    {
        var results = new List<PostingInfo>();
        var client = GetOzonClient(shop);
        var request = new RestRequest(GetPostingUrl, Method.Post);
        var requestBody = new
        {
            Filter = new
            {
                since = DateTimeOffset.UtcNow.AddHours(-10).ToUniversalTime().ToString(OzonDateTimeFormat),
                to = DateTimeOffset.UtcNow.ToUniversalTime().ToString(OzonDateTimeFormat)
            },
            With = new
            {
                analytics_data = false,
                barcodes = false,
                financial_data = false,
                translit = false
            },
            limit = 1000
        };

        request.AddBody(requestBody);
        var response = await client.ExecuteAsync<PostingDtoWrapper>(request, Method.Post);
        if (!response.IsSuccessful || response.Data == null)
        {
            logger.LogError(response.ErrorException, "Error: message: {Message}", response.Content);
            throw new ApiException("Не удалось получить статистику из Ozon");
        }

        results.AddRange(response.Data.Result.Postings);
        return results;
    }

    public async Task<List<PromoteItemDto>> GetAvailablePromotes(Shop shop)
    {
        var client = GetOzonClient(shop);
        var request = new RestRequest(GetPromotesUrl);
        var response = await client.ExecuteAsync<PromotesResponseDto>(request);
        if (!response.IsSuccessful || response.Data == null)
        {
            logger.LogError(response.ErrorException, "Error: message: {Message}", response.Content);
            throw new ApiException("Не получилось получить номенклатуры из Ozon");
        }

        return [.. response.Data.Result.Where(r => r.DateStart <= DateTime.UtcNow && r.DateEnd >= DateTime.UtcNow)];
    }

    public async Task DeletePromote(Shop shop, long ozonId, int promoteId)
    {
        var client = GetOzonClient(shop);
        var request = new RestRequest(DeleteProductFromPromotes, Method.Post);
        var body = new DeactivatePromotionRequestDto(ozonId, promoteId);
        request.AddBody(body);
        var response = await client.ExecuteAsync<DeactivatePromotionResponseDto>(request);
        if (!response.IsSuccessful || response.Data == null)
        {
            logger.LogError(response.ErrorException, "Error: message: {Message}", response.Content);
            throw new ApiException("Проблемы при удалении акции");
        }

        if (response.Data.Result.Rejected.Count > 0)
        {
            throw new BadRequestException(
                $"Акция не была удалена по причине {response.Data.Result.Rejected[0].Reason}");
        }
    }
}

public class DeactivatePromotionResponseDto
{
    public DeactivatePromotionResultDto Result { get; set; } = null!;
}

public class DeactivatePromotionResultDto
{
    public List<RejectedProductDto> Rejected { get; set; } = [];
}

public class RejectedProductDto
{
    [JsonPropertyName("product_id")]
    public long ProductId { get; set; }

    public string Reason { get; set; } = string.Empty;
}

public class DeactivatePromotionRequestDto(long id, int promoteId)
{
    [JsonPropertyName("action_id")]
    public int PromotionId { get; set; } = promoteId;

    [JsonPropertyName("product_ids")]
    public List<long> ProductIds { get; set; } = [id];
}

public class PostingDtoWrapper
{
    [JsonPropertyName("result")]
    public Result Result { get; set; }
}

public class Result
{
    [JsonPropertyName("postings")]
    public List<PostingInfo> Postings { get; set; }

    [JsonPropertyName("has_next")]
    public bool HasNext { get; set; }
}

public class PostingInfo
{
    [JsonPropertyName("posting_number")]
    public string PostingNumber { get; set; }

    [JsonPropertyName("order_id")]
    public long OrderId { get; set; }

    [JsonPropertyName("order_number")]
    public string OrderNumber { get; set; }

    [JsonPropertyName("pickup_code_verified_at")]
    public DateTime? PickupCodeVerifiedAt { get; set; }

    [JsonPropertyName("status")]
    public string Status { get; set; }

    [JsonPropertyName("substatus")]
    public string Substatus { get; set; }

    [JsonPropertyName("tracking_number")]
    public string TrackingNumber { get; set; }

    [JsonPropertyName("tpl_integration_type")]
    public string TplIntegrationType { get; set; }

    [JsonPropertyName("in_process_at")]
    public DateTime? InProcessAt { get; set; }

    [JsonPropertyName("shipment_date")]
    public DateTime? ShipmentDate { get; set; }

    [JsonPropertyName("delivering_date")]
    public DateTime? DeliveringDate { get; set; }

    [JsonPropertyName("customer")]
    public object Customer { get; set; }

    [JsonPropertyName("products")]
    public List<Product> Products { get; set; }

    [JsonPropertyName("addressee")]
    public object Addressee { get; set; }

    [JsonPropertyName("barcodes")]
    public object Barcodes { get; set; }

    [JsonPropertyName("analytics_data")]
    public object AnalyticsData { get; set; }

    [JsonPropertyName("financial_data")]
    public object FinancialData { get; set; }

    [JsonPropertyName("is_express")]
    public bool IsExpress { get; set; }

    [JsonPropertyName("quantum_id")]
    public int QuantumId { get; set; }
}

public class Product
{
    [JsonPropertyName("price")]
    public string Price { get; set; }

    [JsonPropertyName("currency_code")]
    public string CurrencyCode { get; set; }

    [JsonPropertyName("offer_id")]
    public string OfferId { get; set; }

    [JsonPropertyName("name")]
    public string Name { get; set; }

    [JsonPropertyName("sku")]
    public long Sku { get; set; }

    [JsonPropertyName("quantity")]
    public int Quantity { get; set; }

    [JsonPropertyName("mandatory_mark")]
    public List<string> MandatoryMark { get; set; }
}

public class Tariffication
{
    [JsonPropertyName("current_tariff_rate")]
    public int CurrentTariffRate { get; set; }

    [JsonPropertyName("current_tariff_type")]
    public string CurrentTariffType { get; set; }

    [JsonPropertyName("current_tariff_charge")]
    public string CurrentTariffCharge { get; set; }

    [JsonPropertyName("current_tariff_charge_currency_code")]
    public string CurrentTariffChargeCurrencyCode { get; set; }

    [JsonPropertyName("next_tariff_rate")]
    public int NextTariffRate { get; set; }

    [JsonPropertyName("next_tariff_type")]
    public string NextTariffType { get; set; }

    [JsonPropertyName("next_tariff_charge")]
    public string NextTariffCharge { get; set; }

    [JsonPropertyName("next_tariff_charge_currency_code")]
    public string NextTariffChargeCurrencyCode { get; set; }
}

public class FinanceTransactionResponse
{
    [JsonPropertyName("result")]
    public FinanceTransactionResult Result { get; set; }
}

public class FinanceTransactionResult
{
    [JsonPropertyName("operations")]
    public List<Operation> Operations { get; set; }

    [JsonPropertyName("page_count")]
    public int PageCount { get; set; }

    [JsonPropertyName("row_count")]
    public int RowCount { get; set; }
}

public class Operation
{
    [JsonPropertyName("operation_id")]
    public long OperationId { get; set; }

    [JsonPropertyName("operation_type")]
    public string OperationType { get; set; }

    [JsonPropertyName("operation_date")]
    public string OperationDate { get; set; }

    [JsonPropertyName("operation_type_name")]
    public string OperationTypeName { get; set; }

    [JsonPropertyName("delivery_charge")]
    public decimal DeliveryCharge { get; set; }

    [JsonPropertyName("return_delivery_charge")]
    public decimal ReturnDeliveryCharge { get; set; }

    [JsonPropertyName("accruals_for_sale")]
    public decimal AccrualsForSale { get; set; }

    [JsonPropertyName("sale_commission")]
    public decimal SaleCommission { get; set; }

    [JsonPropertyName("amount")]
    public decimal Amount { get; set; }

    [JsonPropertyName("type")]
    public string Type { get; set; }

    [JsonPropertyName("items")]
    public List<OperationItem> Items { get; set; } = [];
}

public class OperationItem
{
    public string Name { get; set; }
    public int Sku { get; set; }
}

public class UpdatePricesResponseDto
{
    public List<UpdatePricesResultDto> Result { get; set; }
}

public class UpdatePricesResultDto
{
    [JsonPropertyName("offer_id")]
    public string OfferId { get; set; } = null!;

    public bool Updated { get; set; }
}

public class OzonStockResultDto
{
    public List<InternalOzonStockDto> Items { get; set; } = [];
    public int Total { get; set; }
    public string Cursor { get; set; } = null!;
}

public class InternalOzonStockDto
{
    [JsonPropertyName("product_id")]
    public long ProductId { get; set; }

    public List<OzonStockTypeDto> Stocks { get; set; }
}

public class OzonStockTypeDto
{
    public string Type { get; set; } = null!;
    public int Present { get; set; }
    public int Reserved { get; set; }
}

public class OzonWarehousesDtoWrapper
{
    public List<InternalWarehouses> Result { get; set; } = null!;

    public class InternalWarehouses
    {
        [JsonPropertyName("warehouse_id")]
        public long WarehouseId { get; set; }

        public string Status { get; set; } = null!;
    }
}

public class PricesResponseDto
{
    public List<PriceItemDto> Items { get; set; } = [];
}

public class PriceItemDto
{
    [JsonPropertyName("product_id")]
    public long ProductId { get; set; }

    [JsonPropertyName("offer_id")]
    public string OfferId { get; set; } = null!;

    public decimal Acquiring { get; set; }
    public PriceDto Price { get; set; } = null!;
    public OzonCommissionsDto Commissions { get; set; } = null!;

    [JsonPropertyName("marketing_actions")]
    public MarketingPromotesDto MarketingPromotes { get; set; } = null!;
}

public class MarketingPromotesDto
{
    public List<MarketingPromoteDto> Actions { get; set; }
}

public class MarketingPromoteDto
{
    public string Title { get; set; } = null!;
    public decimal Value { get; set; }

    [JsonPropertyName("date_from")]
    public DateTime DateFrom { get; set; }

    [JsonPropertyName("date_to")]
    public DateTime DateTo { get; set; }
}

public class PriceDto
{
    [JsonPropertyName("currency_code")]
    public string CurrencyCode { get; set; } = null!;

    public decimal Price { get; set; }

    [JsonPropertyName("old_price")]
    public decimal OldPrice { get; set; }

    [JsonPropertyName("retail_price")]
    public decimal RetailPrice { get; set; }

    public decimal Vat { get; set; }

    [JsonPropertyName("min_price")]
    public decimal MinPrice { get; set; }

    [JsonPropertyName("marketing_price")]
    public decimal MarketingPrice { get; set; }

    [JsonPropertyName("marketing_seller_price")]
    public decimal MarketingSellerPrice { get; set; }

    [JsonPropertyName("auto_action_enabled")]
    public bool AutoActionEnabled { get; set; }
}

public class PriceIndexes
{
    [JsonPropertyName("external_index_data")]
    public IndexData ExternalIndexData { get; set; }

    [JsonPropertyName("ozon_index_data")]
    public IndexData OzonIndexData { get; set; }

    [JsonPropertyName("price_index")]
    public string PriceIndex { get; set; }

    [JsonPropertyName("self_marketplaces_index_data")]
    public IndexData SelfMarketplacesIndexData { get; set; }
}

public class IndexData
{
    [JsonPropertyName("minimal_price")]
    public string MinimalPrice { get; set; }

    [JsonPropertyName("minimal_price_currency")]
    public string MinimalPriceCurrency { get; set; }

    [JsonPropertyName("price_index_value")]
    public int PriceIndexValue { get; set; }
}

public class OzonCommissionsDto
{
    [JsonPropertyName("sales_percent")]
    public decimal SalesPercent { get; set; }

    [JsonPropertyName("sales_percent_fbo")]
    public decimal SalesPercentFbo { get; set; }

    [JsonPropertyName("sales_percent_fbs")]
    public decimal SalesPercentFbs { get; set; }

    [JsonPropertyName("fbo_fulfillment_amount")]
    public decimal FboFulfillmentAmount { get; set; }

    [JsonPropertyName("fbo_direct_flow_trans_min_amount")]
    public decimal FboDirectFlowTransMinAmount { get; set; }

    [JsonPropertyName("fbo_direct_flow_trans_max_amount")]
    public decimal FboDirectFlowTransMaxAmount { get; set; }

    [JsonPropertyName("fbo_deliv_to_customer_amount")]
    public decimal FboDelivToCustomerAmount { get; set; }

    [JsonPropertyName("fbo_return_flow_amount")]
    public decimal FboReturnFlowAmount { get; set; }

    [JsonPropertyName("fbo_return_flow_trans_min_amount")]
    public decimal FboReturnFlowTransMinAmount { get; set; }

    [JsonPropertyName("fbo_return_flow_trans_max_amount")]
    public decimal FboReturnFlowTransMaxAmount { get; set; }

    [JsonPropertyName("fbs_first_mile_min_amount")]
    public decimal FbsFirstMileMinAmount { get; set; }

    [JsonPropertyName("fbs_first_mile_max_amount")]
    public decimal FbsFirstMileMaxAmount { get; set; }

    [JsonPropertyName("fbs_direct_flow_trans_min_amount")]
    public decimal FbsDirectFlowTransMinAmount { get; set; }

    [JsonPropertyName("fbs_direct_flow_trans_max_amount")]
    public decimal FbsDirectFlowTransMaxAmount { get; set; }

    [JsonPropertyName("fbs_deliv_to_customer_amount")]
    public decimal FbsDelivToCustomerAmount { get; set; }

    [JsonPropertyName("fbs_return_flow_amount")]
    public decimal FbsReturnFlowAmount { get; set; }

    [JsonPropertyName("fbs_return_flow_trans_min_amount")]
    public decimal FbsReturnFlowTransMinAmount { get; set; }

    [JsonPropertyName("fbs_return_flow_trans_max_amount")]
    public decimal FbsReturnFlowTransMaxAmount { get; set; }
}

public class RootAttribute
{
    [JsonPropertyName("result")]
    public List<ResultAttributes> Result { get; set; } = [];

    [JsonPropertyName("total")]
    public int Total { get; set; }

    [JsonPropertyName("last_id")]
    public string LastId { get; set; } = null!;
}

public class ResultAttributes
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("barcode")]
    public string Barcode { get; set; } = null!;

    [JsonPropertyName("name")]
    public string Name { get; set; } = null!;

    [JsonPropertyName("offer_id")]
    public string OfferId { get; set; } = null!;

    [JsonPropertyName("height")]
    public int Height { get; set; }

    [JsonPropertyName("depth")]
    public int Depth { get; set; }

    [JsonPropertyName("width")]
    public int Width { get; set; }

    [JsonPropertyName("dimension_unit")]
    public string DimensionUnit { get; set; } = null!;

    [JsonPropertyName("weight")]
    public int Weight { get; set; }

    [JsonPropertyName("weight_unit")]
    public string WeightUnit { get; set; } = null!;

    [JsonPropertyName("description_category_id")]
    public int DescriptionCategoryId { get; set; }

    [JsonPropertyName("type_id")]
    public int TypeId { get; set; }

    [JsonPropertyName("primary_image")]
    public string PrimaryImage { get; set; } = null!;

    [JsonPropertyName("model_info")]
    public ModelInfoAttribute ModelInfo { get; set; } = null!;

    [JsonPropertyName("pdf_list")]
    public List<object> PdfList { get; set; } = [];

    [JsonPropertyName("attributes")]
    public List<Attribute> Attributes { get; set; } = [];

    [JsonPropertyName("complex_attributes")]
    public List<object> ComplexAttributes { get; set; } = [];

    [JsonPropertyName("color_image")]
    public string ColorImage { get; set; } = null!;
}

public class ModelInfoAttribute
{
    [JsonPropertyName("model_id")]
    public int ModelId { get; set; }

    [JsonPropertyName("count")]
    public int Count { get; set; }
}

public class Attribute
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("complex_id")]
    public int ComplexId { get; set; }

    [JsonPropertyName("values")]
    public List<ValueAttribute> Values { get; set; } = [];
}

public class ValueAttribute
{
    [JsonPropertyName("dictionary_value_id")]
    public int DictionaryValueId { get; set; }

    [JsonPropertyName("value")]
    public string Value { get; set; } = null!;
}