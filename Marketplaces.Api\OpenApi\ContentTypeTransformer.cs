using Microsoft.AspNetCore.OpenApi;
using Microsoft.OpenApi.Models;

namespace Marketplaces.Api.OpenApi;

public sealed class ContentTypeTransformer : IOpenApiDocumentTransformer
{
    public Task TransformAsync(OpenApiDocument document, OpenApiDocumentTransformerContext context, CancellationToken cancellationToken)
    {
        // Remove unwanted content types, keep only application/json
        foreach (var pathItem in document.Paths.Values)
        {
            foreach (var operation in pathItem.Operations.Values)
            {
                // Handle request body content types
                if (operation.RequestBody?.Content != null)
                {
                    var contentToRemove = operation.RequestBody.Content.Keys
                        .Where(key => key != "application/json")
                        .ToList();

                    foreach (var contentType in contentToRemove)
                    {
                        operation.RequestBody.Content.Remove(contentType);
                    }
                }

                // Handle response content types
                foreach (var response in operation.Responses.Values)
                {
                    if (response.Content != null)
                    {
                        var contentToRemove = response.Content.Keys
                            .Where(key => key != "application/json")
                            .ToList();

                        foreach (var contentType in contentToRemove)
                        {
                            response.Content.Remove(contentType);
                        }
                    }
                }
            }
        }

        return Task.CompletedTask;
    }
}
