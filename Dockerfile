FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build

WORKDIR /source
COPY nuget.config .
COPY Marketplaces.Api/Marketplaces.Api.csproj Marketplaces.Api/

# Copy source and build
COPY Marketplaces.Api/ ./Marketplaces.Api/
WORKDIR /source/Marketplaces.Api
RUN dotnet build -c release
RUN dotnet publish -c release --no-build -o /app

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime
WORKDIR /app
COPY --from=build /app .
ENTRYPOINT ["dotnet", "Marketplaces.Api.dll"]
