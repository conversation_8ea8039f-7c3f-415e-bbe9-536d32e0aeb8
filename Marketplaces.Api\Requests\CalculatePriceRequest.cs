using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Marketplaces.Api.Models;

namespace Marketplaces.Api.Requests;

public class CalculatePriceRequest
{
    [Required]
    public decimal PurchasePrice { get; set; }

    [Required]
    public decimal BasePrice { get; set; }

    [Required]
    public decimal DiscountPercent { get; set; }

    [Required]
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public Marketplace Marketplace { get; set; }
}
